import React, { useState, useEffect } from 'react';
import type { UploadedVideo } from '../../types/video';

interface TrimModalProps {
  video: UploadedVideo | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (videoId: string, trimStart: number, trimEnd: number) => void;
}

export const TrimModal: React.FC<TrimModalProps> = ({
  video,
  isOpen,
  onClose,
  onSave
}) => {
  const [trimStart, setTrimStart] = useState(0);
  const [trimEnd, setTrimEnd] = useState(0);

  useEffect(() => {
    if (video) {
      setTrimStart(video.trimStart || 0);
      setTrimEnd(video.trimEnd || video.durationInFrames);
    }
  }, [video]);

  if (!isOpen || !video) return null;

  const handleSave = () => {
    onSave(video.id, trimStart, trimEnd);
    onClose();
  };

  const handleReset = () => {
    setTrimStart(0);
    setTrimEnd(video.durationInFrames);
  };

  const formatTime = (frame: number) => {
    const seconds = frame / 30;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = (seconds % 60).toFixed(1);
    return `${minutes}:${remainingSeconds.padStart(4, '0')}`;
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '20px',
        minWidth: '400px',
        maxWidth: '500px'
      }}>
        <h3 style={{ margin: '0 0 20px 0' }}>Trim Video: {video.name}</h3>
        
        <div style={{ marginBottom: '20px' }}>
          <p style={{ fontSize: '14px', color: '#666', margin: '0 0 10px 0' }}>
            Original Duration: {formatTime(video.durationInFrames)} ({video.durationInFrames} frames)
          </p>
          <p style={{ fontSize: '14px', color: '#666', margin: '0 0 10px 0' }}>
            Trimmed Duration: {formatTime(trimEnd - trimStart)} ({trimEnd - trimStart} frames)
          </p>
        </div>

        {/* Trim Start */}
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontSize: '14px', fontWeight: 'bold' }}>
            Start Frame: {trimStart} ({formatTime(trimStart)})
          </label>
          <input
            type="range"
            min="0"
            max={video.durationInFrames - 1}
            value={trimStart}
            onChange={(e) => {
              const value = parseInt(e.target.value);
              setTrimStart(Math.min(value, trimEnd - 1));
            }}
            style={{ width: '100%' }}
          />
          <input
            type="number"
            min="0"
            max={video.durationInFrames - 1}
            value={trimStart}
            onChange={(e) => {
              const value = parseInt(e.target.value) || 0;
              setTrimStart(Math.min(Math.max(0, value), trimEnd - 1));
            }}
            style={{
              width: '80px',
              padding: '4px',
              marginTop: '5px',
              border: '1px solid #ccc',
              borderRadius: '4px'
            }}
          />
        </div>

        {/* Trim End */}
        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontSize: '14px', fontWeight: 'bold' }}>
            End Frame: {trimEnd} ({formatTime(trimEnd)})
          </label>
          <input
            type="range"
            min="1"
            max={video.durationInFrames}
            value={trimEnd}
            onChange={(e) => {
              const value = parseInt(e.target.value);
              setTrimEnd(Math.max(value, trimStart + 1));
            }}
            style={{ width: '100%' }}
          />
          <input
            type="number"
            min="1"
            max={video.durationInFrames}
            value={trimEnd}
            onChange={(e) => {
              const value = parseInt(e.target.value) || video.durationInFrames;
              setTrimEnd(Math.max(Math.min(video.durationInFrames, value), trimStart + 1));
            }}
            style={{
              width: '80px',
              padding: '4px',
              marginTop: '5px',
              border: '1px solid #ccc',
              borderRadius: '4px'
            }}
          />
        </div>

        {/* Buttons */}
        <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
          <button
            onClick={handleReset}
            style={{
              padding: '8px 16px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              backgroundColor: 'white',
              cursor: 'pointer'
            }}
          >
            Reset
          </button>
          <button
            onClick={onClose}
            style={{
              padding: '8px 16px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              backgroundColor: 'white',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            style={{
              padding: '8px 16px',
              border: 'none',
              borderRadius: '4px',
              backgroundColor: '#007bff',
              color: 'white',
              cursor: 'pointer'
            }}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

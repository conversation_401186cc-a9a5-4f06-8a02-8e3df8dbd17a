import React from 'react';
import { UploadedVideo } from '../../types/video';

interface TimelineClipProps {
  video: UploadedVideo;
  index: number;
  width: number;
  left: number;
  onDelete: (videoId: string) => void;
}

export const TimelineClip: React.FC<TimelineClipProps> = ({
  video,
  index,
  width,
  left,
  onDelete
}) => {
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent timeline click
    onDelete(video.id);
  };

  return (
    <div
      className="timeline-clip"
      style={{
        position: 'absolute',
        left: `${left}px`,
        width: `${width}px`,
        height: '80px',
        top: '10px',
        backgroundColor: '#4CAF50',
        border: '1px solid #45a049',
        borderRadius: '4px',
        padding: '4px',
        boxSizing: 'border-box',
        cursor: 'grab',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}
    >
      {/* Clip header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        fontSize: '10px',
        color: 'white',
        fontWeight: 'bold'
      }}>
        <span style={{ 
          overflow: 'hidden', 
          textOverflow: 'ellipsis', 
          whiteSpace: 'nowrap',
          flex: 1,
          marginRight: '4px'
        }}>
          {index + 1}. {video.name}
        </span>
        <button
          onClick={handleDelete}
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            border: 'none',
            borderRadius: '2px',
            color: 'white',
            fontSize: '8px',
            padding: '1px 3px',
            cursor: 'pointer',
            lineHeight: '1'
          }}
          title="Delete clip"
        >
          ✕
        </button>
      </div>

      {/* Clip info */}
      <div style={{
        fontSize: '8px',
        color: 'rgba(255, 255, 255, 0.9)',
        textAlign: 'center'
      }}>
        {video.duration.toFixed(1)}s
      </div>
    </div>
  );
};

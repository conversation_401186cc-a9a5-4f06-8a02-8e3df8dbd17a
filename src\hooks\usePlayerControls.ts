import { useState, useEffect, useRef } from 'react';
import type { PlayerRef } from '@remotion/player';

export const usePlayerControls = (totalDurationInFrames: number) => {
  const [currentFrame, setCurrentFrame] = useState(0);
  const playerRef = useRef<PlayerRef>(null);

  // Update current frame based on player position
  useEffect(() => {
    const player = playerRef.current;
    if (!player) return;

    const updateFrame = () => {
      try {
        const frame = player.getCurrentFrame();
        setCurrentFrame(frame);
      } catch (error) {
        // Handle case where getCurrentFrame might not be available
        console.log('getCurrentFrame not available');
      }
    };

    // Set up interval to track playback position
    const interval = setInterval(updateFrame, 100); // Update every 100ms

    return () => clearInterval(interval);
  }, [totalDurationInFrames]);

  const seekToFrame = (frame: number) => {
    const player = playerRef.current;
    if (player) {
      try {
        player.seekTo(frame);
        setCurrentFrame(frame);
      } catch (error) {
        console.error('Error seeking to frame:', error);
      }
    }
  };

  return {
    playerRef,
    currentFrame,
    setCurrentFrame,
    seekToFrame
  };
};

import React, { useState, useEffect } from 'react';
import { PlayerRef } from '@remotion/player';

interface PlayerControlsProps {
  playerRef: React.RefObject<PlayerRef | null>;
  totalDurationInFrames: number;
  currentFrame: number;
  onFrameChange: (frame: number) => void;
}

export const PlayerControls: React.FC<PlayerControlsProps> = ({
  playerRef,
  totalDurationInFrames,
  currentFrame,
  onFrameChange
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(1);

  // Sync playing state with player
  useEffect(() => {
    const player = playerRef.current;
    if (!player) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    // Listen to player events if available
    // Note: We'll need to check if these events exist in the Remotion player
    try {
      player.addEventListener?.('play', handlePlay);
      player.addEventListener?.('pause', handlePause);
    } catch (error) {
      // Fallback if events aren't available
      console.log('Player events not available, using manual state management');
    }

    return () => {
      try {
        player.removeEventListener?.('play', handlePlay);
        player.removeEventListener?.('pause', handlePause);
      } catch (error) {
        // Ignore cleanup errors
      }
    };
  }, [playerRef]);

  const handlePlayPause = () => {
    const player = playerRef.current;
    if (!player) return;

    if (isPlaying) {
      player.pause();
      setIsPlaying(false);
    } else {
      player.play();
      setIsPlaying(true);
    }
  };

  const handleSeek = (event: React.ChangeEvent<HTMLInputElement>) => {
    const frame = parseInt(event.target.value);
    const player = playerRef.current;
    
    if (player) {
      player.seekTo(frame);
      onFrameChange(frame);
    }
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    
    const player = playerRef.current;
    if (player) {
      player.setVolume(newVolume);
    }
  };

  const formatTime = (frame: number) => {
    const seconds = frame / 30; // 30fps
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="player-controls" style={{
      display: 'flex',
      alignItems: 'center',
      gap: '15px',
      padding: '10px',
      backgroundColor: '#f5f5f5',
      borderRadius: '4px',
      marginBottom: '10px'
    }}>
      {/* Play/Pause Button */}
      <button
        onClick={handlePlayPause}
        style={{
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '14px',
          minWidth: '60px'
        }}
      >
        {isPlaying ? '⏸️ Pause' : '▶️ Play'}
      </button>

      {/* Time Display */}
      <div style={{ fontSize: '12px', color: '#666', minWidth: '80px' }}>
        {formatTime(currentFrame)} / {formatTime(totalDurationInFrames)}
      </div>

      {/* Seek Bar */}
      <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: '10px' }}>
        <span style={{ fontSize: '12px', color: '#666' }}>Seek:</span>
        <input
          type="range"
          min="0"
          max={totalDurationInFrames}
          value={currentFrame}
          onChange={handleSeek}
          style={{
            flex: 1,
            height: '4px',
            borderRadius: '2px',
            background: '#ddd',
            outline: 'none',
            cursor: 'pointer'
          }}
        />
      </div>

      {/* Volume Control */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', minWidth: '120px' }}>
        <span style={{ fontSize: '12px', color: '#666' }}>🔊</span>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={volume}
          onChange={handleVolumeChange}
          style={{
            width: '80px',
            height: '4px',
            borderRadius: '2px',
            background: '#ddd',
            outline: 'none',
            cursor: 'pointer'
          }}
        />
        <span style={{ fontSize: '10px', color: '#666', minWidth: '25px' }}>
          {Math.round(volume * 100)}%
        </span>
      </div>
    </div>
  );
};

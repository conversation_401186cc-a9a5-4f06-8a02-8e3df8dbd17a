import React from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import type { UploadedVideo } from '../../types/video';
import { DraggableTimelineClip } from './DraggableTimelineClip';
import { PlaybackIndicator } from './PlaybackIndicator';

interface DraggableTimelineProps {
  videos: UploadedVideo[];
  totalDurationInFrames: number;
  currentFrame: number;
  onReorderVideos: (newOrder: UploadedVideo[]) => void;
  onDeleteVideo: (videoId: string) => void;
  onSeek: (frame: number) => void;
  onTrimVideo?: (video: UploadedVideo) => void;
}

export const DraggableTimeline: React.FC<DraggableTimelineProps> = ({
  videos,
  totalDurationInFrames,
  currentFrame,
  onReorderVideos,
  onDeleteVideo,
  onSeek,
  onTrimVideo
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const timelineWidth = 800;
  const timelineHeight = 100;

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = videos.findIndex(video => video.id === active.id);
      const newIndex = videos.findIndex(video => video.id === over?.id);

      const newOrder = arrayMove(videos, oldIndex, newIndex);
      onReorderVideos(newOrder);
    }
  };

  const handleTimelineClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickRatio = clickX / timelineWidth;
    const targetFrame = Math.round(clickRatio * totalDurationInFrames);
    onSeek(Math.max(0, Math.min(targetFrame, totalDurationInFrames)));
  };

  return (
    <div className="draggable-timeline-container">
      <div className="timeline-header" style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '10px'
      }}>
        <h3 style={{ margin: 0, fontSize: '16px' }}>Timeline</h3>
        <div style={{ fontSize: '12px', color: '#666' }}>
          Duration: {(totalDurationInFrames / 30).toFixed(1)}s | Frame: {currentFrame}/{totalDurationInFrames}
        </div>
      </div>
      
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <div 
          className="timeline-track"
          style={{
            width: timelineWidth,
            height: timelineHeight,
            position: 'relative',
            backgroundColor: '#f0f0f0',
            border: '1px solid #ccc',
            borderRadius: '4px',
            cursor: 'pointer',
            overflow: 'hidden'
          }}
          onClick={handleTimelineClick}
        >
          <SortableContext items={videos.map(v => v.id)} strategy={horizontalListSortingStrategy}>
            {videos.map((video, index) => {
              // Calculate position and width based on duration
              let startFrame = 0;
              for (let i = 0; i < index; i++) {
                startFrame += videos[i].durationInFrames;
              }
              
              const clipWidth = totalDurationInFrames > 0 
                ? (video.durationInFrames / totalDurationInFrames) * timelineWidth 
                : 0;
              const clipLeft = totalDurationInFrames > 0 
                ? (startFrame / totalDurationInFrames) * timelineWidth 
                : 0;

              return (
                <DraggableTimelineClip
                  key={video.id}
                  video={video}
                  index={index}
                  width={clipWidth}
                  left={clipLeft}
                  onDelete={onDeleteVideo}
                  onTrim={onTrimVideo}
                />
              );
            })}
          </SortableContext>

          {/* Playback position indicator */}
          <PlaybackIndicator
            currentFrame={currentFrame}
            totalDurationInFrames={totalDurationInFrames}
            timelineWidth={timelineWidth}
          />
        </div>
      </DndContext>
    </div>
  );
};

import React from 'react';

interface PlaybackIndicatorProps {
  currentFrame: number;
  totalDurationInFrames: number;
  timelineWidth: number;
}

export const PlaybackIndicator: React.FC<PlaybackIndicatorProps> = ({
  currentFrame,
  totalDurationInFrames,
  timelineWidth
}) => {
  const position = totalDurationInFrames > 0 
    ? (currentFrame / totalDurationInFrames) * timelineWidth 
    : 0;

  return (
    <div
      className="playback-indicator"
      style={{
        position: 'absolute',
        left: `${position}px`,
        top: '0',
        width: '2px',
        height: '100%',
        backgroundColor: '#ff4444',
        zIndex: 10,
        pointerEvents: 'none'
      }}
    >
      {/* Indicator handle */}
      <div
        style={{
          position: 'absolute',
          top: '-4px',
          left: '-4px',
          width: '10px',
          height: '8px',
          backgroundColor: '#ff4444',
          borderRadius: '2px'
        }}
      />
    </div>
  );
};

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { UploadedVideo } from '../../types/video';

interface DraggableTimelineClipProps {
  video: UploadedVideo;
  index: number;
  width: number;
  left: number;
  onDelete: (videoId: string) => void;
  onTrim?: (video: UploadedVideo) => void;
}

export const DraggableTimelineClip: React.FC<DraggableTimelineClipProps> = ({
  video,
  index,
  width,
  left,
  onDelete,
  onTrim
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: video.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent timeline click and drag
    onDelete(video.id);
  };

  const handleTrim = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent timeline click and drag
    if (onTrim) {
      onTrim(video);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={{
        ...style,
        position: 'absolute',
        left: `${left}px`,
        width: `${width}px`,
        height: '80px',
        top: '10px',
        backgroundColor: isDragging ? '#45a049' : '#4CAF50',
        border: '1px solid #45a049',
        borderRadius: '4px',
        padding: '4px',
        boxSizing: 'border-box',
        cursor: isDragging ? 'grabbing' : 'grab',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        zIndex: isDragging ? 1000 : 1,
      }}
      {...attributes}
      {...listeners}
    >
      {/* Clip header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        fontSize: '10px',
        color: 'white',
        fontWeight: 'bold',
        pointerEvents: 'none' // Prevent interference with drag
      }}>
        <span style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          flex: 1,
          marginRight: '4px'
        }}>
          {index + 1}. {video.name}
        </span>
        <div style={{ display: 'flex', gap: '2px', pointerEvents: 'auto' }}>
          {onTrim && (
            <button
              onClick={handleTrim}
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                border: 'none',
                borderRadius: '2px',
                color: 'white',
                fontSize: '8px',
                padding: '1px 3px',
                cursor: 'pointer',
                lineHeight: '1'
              }}
              title="Trim clip"
            >
              ✂️
            </button>
          )}
          <button
            onClick={handleDelete}
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              border: 'none',
              borderRadius: '2px',
              color: 'white',
              fontSize: '8px',
              padding: '1px 3px',
              cursor: 'pointer',
              lineHeight: '1'
            }}
            title="Delete clip"
          >
            ✕
          </button>
        </div>
      </div>

      {/* Clip info */}
      <div style={{
        fontSize: '8px',
        color: 'rgba(255, 255, 255, 0.9)',
        textAlign: 'center',
        pointerEvents: 'none'
      }}>
        {video.duration.toFixed(1)}s
      </div>

      {/* Drag handle indicator */}
      <div style={{
        position: 'absolute',
        left: '2px',
        top: '50%',
        transform: 'translateY(-50%)',
        width: '4px',
        height: '20px',
        backgroundColor: 'rgba(255, 255, 255, 0.3)',
        borderRadius: '2px',
        pointerEvents: 'none'
      }} />
    </div>
  );
};

import React from 'react';
import type { UploadedVideo } from '../../types/video';
import { TimelineClip } from './TimelineClip';
import { PlaybackIndicator } from './PlaybackIndicator';

interface TimelineProps {
  videos: UploadedVideo[];
  totalDurationInFrames: number;
  currentFrame: number;
  onReorderVideos: (newOrder: UploadedVideo[]) => void;
  onDeleteVideo: (videoId: string) => void;
  onSeek: (frame: number) => void;
}

export const Timeline: React.FC<TimelineProps> = ({
  videos,
  totalDurationInFrames,
  currentFrame,
  onReorderVideos,
  onDeleteVideo,
  onSeek
}) => {
  const timelineWidth = 800; // Fixed width for now
  const timelineHeight = 100;

  const handleTimelineClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickRatio = clickX / timelineWidth;
    const targetFrame = Math.round(clickRatio * totalDurationInFrames);
    onSeek(Math.max(0, Math.min(targetFrame, totalDurationInFrames)));
  };

  return (
    <div className="timeline-container">
      <div className="timeline-header">
        <h3>Timeline</h3>
        <div className="timeline-info">
          Duration: {(totalDurationInFrames / 30).toFixed(1)}s | Frame: {currentFrame}/{totalDurationInFrames}
        </div>
      </div>
      
      <div 
        className="timeline-track"
        style={{
          width: timelineWidth,
          height: timelineHeight,
          position: 'relative',
          backgroundColor: '#f0f0f0',
          border: '1px solid #ccc',
          borderRadius: '4px',
          cursor: 'pointer',
          overflow: 'hidden'
        }}
        onClick={handleTimelineClick}
      >
        {/* Video clips */}
        {videos.map((video, index) => {
          // Calculate position and width based on duration
          let startFrame = 0;
          for (let i = 0; i < index; i++) {
            startFrame += videos[i].durationInFrames;
          }
          
          const clipWidth = totalDurationInFrames > 0 
            ? (video.durationInFrames / totalDurationInFrames) * timelineWidth 
            : 0;
          const clipLeft = totalDurationInFrames > 0 
            ? (startFrame / totalDurationInFrames) * timelineWidth 
            : 0;

          return (
            <TimelineClip
              key={video.id}
              video={video}
              index={index}
              width={clipWidth}
              left={clipLeft}
              onDelete={onDeleteVideo}
            />
          );
        })}

        {/* Playback position indicator */}
        <PlaybackIndicator
          currentFrame={currentFrame}
          totalDurationInFrames={totalDurationInFrames}
          timelineWidth={timelineWidth}
        />
      </div>
    </div>
  );
};

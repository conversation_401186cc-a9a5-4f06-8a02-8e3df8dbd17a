import { useState, useEffect } from 'react';
import type { UploadedVideo } from '../types/video.js';
import { getVideoDuration, calculateDynamicDuration } from '../utils/videoUtils.js';

/**
 * Custom hook for managing video upload and state
 */
export const useVideoManager = () => {
  const [uploadedVideos, setUploadedVideos] = useState<UploadedVideo[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [playerDuration, setPlayerDuration] = useState(30); // Default 1 second

  /**
   * Handle file selection and processing
   */
  const handleFileSelection = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsProcessing(true);
    const newVideos: UploadedVideo[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const duration = await getVideoDuration(file);
        const durationInFrames = Math.round(duration * 30); // 30fps

        const videoData: UploadedVideo = {
          id: `video-${Date.now()}-${i}`,
          file,
          name: file.name,
          src: URL.createObjectURL(file),
          duration,
          durationInFrames
        };

        newVideos.push(videoData);
      }

      setUploadedVideos(prev => [...prev, ...newVideos]);
    } catch (error) {
      console.error('Error processing videos:', error);
      alert('Error processing one or more video files. Please try again.');
    } finally {
      setIsProcessing(false);
      // Reset the input
      event.target.value = '';
    }
  };

  /**
   * Remove a video from the list
   */
  const removeVideo = (videoId: string) => {
    setUploadedVideos(prev => {
      const videoToRemove = prev.find(v => v.id === videoId);
      if (videoToRemove) {
        // Clean up the object URL to prevent memory leaks
        URL.revokeObjectURL(videoToRemove.src);
      }
      return prev.filter(v => v.id !== videoId);
    });
  };

  /**
   * Clear all videos
   */
  const clearAllVideos = () => {
    uploadedVideos.forEach(video => {
      URL.revokeObjectURL(video.src);
    });
    setUploadedVideos([]);
  };

  /**
   * Reorder videos in the list
   */
  const reorderVideos = (newOrder: UploadedVideo[]) => {
    setUploadedVideos(newOrder);
  };

  /**
   * Update video trim settings
   */
  const updateVideoTrim = (videoId: string, trimStart: number, trimEnd: number) => {
    setUploadedVideos(prev =>
      prev.map(video =>
        video.id === videoId
          ? { ...video, trimStart, trimEnd }
          : video
      )
    );
  };

  // Effect to calculate dynamic duration using parseMedia for accurate metadata
  useEffect(() => {
    const updatePlayerDuration = async () => {
      const duration = await calculateDynamicDuration(uploadedVideos);
      setPlayerDuration(duration);
    };

    updatePlayerDuration();
  }, [uploadedVideos]);

  return {
    uploadedVideos,
    isProcessing,
    playerDuration,
    handleFileSelection,
    removeVideo,
    clearAllVideos,
    reorderVideos,
    updateVideoTrim
  };
};
